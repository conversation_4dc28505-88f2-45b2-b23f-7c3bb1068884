#!/usr/bin/env python3
"""
Test script for PPE Toggle-Based Conditional Attendance Feature

This script validates the key functionality of the new PPE verification mode
without requiring the full Streamlit interface.
"""

import sys
import os
import time
from unittest.mock import Mock, MagicMock

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ppe_detection_engine():
    """Test PPE detection engine enhancements"""
    print("🧪 Testing PPE Detection Engine...")
    
    try:
        from ppe_detection_engine import PPEDetectionEngine
        
        # Initialize engine
        engine = PPEDetectionEngine()
        
        # Test 1: Check default required PPE configuration
        required_ppe = engine.get_required_ppe_for_attendance()
        expected_ppe = {'Hardhat': True, 'Mask': True, 'Safety Vest': True}
        
        assert required_ppe == expected_ppe, f"Expected {expected_ppe}, got {required_ppe}"
        print("✅ Default PPE configuration correct")
        
        # Test 2: Test setting new PPE configuration
        new_config = {'Hardhat': True, 'Mask': False, 'Safety Vest': True}
        engine.set_required_ppe_for_attendance(new_config)
        updated_config = engine.get_required_ppe_for_attendance()
        
        assert updated_config == new_config, f"Expected {new_config}, got {updated_config}"
        print("✅ PPE configuration update works")
        
        # Test 3: Test PPE compliance checking
        # Mock detection data - person with all required PPE
        person_bbox = [100, 100, 200, 200]
        detections = [
            {'class_name': 'Hardhat', 'bbox': [110, 110, 150, 150]},
            {'class_name': 'Safety Vest', 'bbox': [120, 150, 180, 190]}
        ]

        compliance_result = engine.check_person_ppe_compliance_for_attendance(person_bbox, detections)

        # Should be compliant since only Hardhat and Safety Vest are required in new_config
        assert compliance_result['compliant'] == True, f"Expected compliant=True, got {compliance_result}"
        print("✅ PPE compliance checking works")

        # Test 4: Test non-compliance
        detections_missing = [
            {'class_name': 'Safety Vest', 'bbox': [120, 150, 180, 190]},
            {'class_name': 'NO-Hardhat', 'bbox': [110, 110, 150, 150]}  # Missing required hardhat
        ]

        compliance_result_missing = engine.check_person_ppe_compliance_for_attendance(person_bbox, detections_missing)

        # Should be non-compliant since Hardhat is required but missing
        assert compliance_result_missing['compliant'] == False, f"Expected compliant=False, got {compliance_result_missing}"
        assert 'Hardhat' in compliance_result_missing['missing_ppe'], "Should list Hardhat as missing"
        print("✅ PPE non-compliance detection works")
        
        print("🎉 PPE Detection Engine tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ PPE Detection Engine test failed: {e}")
        return False

def test_webcam_detector_logic():
    """Test webcam detector attendance logic"""
    print("\n🧪 Testing Webcam Detector Logic...")
    
    try:
        from webcam_component import WebcamPPEDetector
        from ppe_detection_engine import PPEDetectionEngine
        
        # Mock attendance manager
        mock_attendance_manager = Mock()
        mock_attendance_manager.record_attendance.return_value = True
        
        # Initialize detector
        engine = PPEDetectionEngine()
        detector = WebcamPPEDetector(engine, mock_attendance_manager)
        
        # Test 1: Face Only Mode (default)
        detector.ppe_verification_mode = False
        
        # Mock face results
        face_results = [{
            'recognized_person': 'John Doe',
            'recognition_confidence': 85,
            'bbox': [100, 100, 200, 200]
        }]
        
        # Mock employee info
        detector._get_employee_cached = Mock(return_value={
            'employee_id': 'EMP001',
            'department': 'Engineering'
        })
        
        # Store initial detection count
        initial_detections = len(detector.recent_detections)
        
        # Process attendance
        detector._process_attendance(face_results)
        
        # Should have recorded attendance
        assert len(detector.recent_detections) > initial_detections, "Should have added detection record"
        latest_detection = detector.recent_detections[-1]
        assert latest_detection['status'] == 'Present', f"Expected Present, got {latest_detection['status']}"
        assert latest_detection['ppe_mode'] == False, "Should be in Face Only mode"
        
        print("✅ Face Only Mode works")
        
        # Test 2: PPE Verification Mode
        detector.ppe_verification_mode = True
        detector._last_detections = [
            {'class_name': 'Hardhat', 'bbox': [110, 110, 150, 150]},
            {'class_name': 'Mask', 'bbox': [130, 120, 160, 140]},
            {'class_name': 'Safety Vest', 'bbox': [120, 150, 180, 190]}
        ]

        # Use different person to avoid cooldown
        face_results_2 = [{
            'recognized_person': 'Jane Smith',
            'recognition_confidence': 90,
            'bbox': [100, 100, 200, 200]
        }]

        # Mock employee info for Jane
        def mock_get_employee(name):
            if name == 'John Doe':
                return {'employee_id': 'EMP001', 'department': 'Engineering'}
            elif name == 'Jane Smith':
                return {'employee_id': 'EMP002', 'department': 'Safety'}
            return None

        detector._get_employee_cached = mock_get_employee

        initial_detections = len(detector.recent_detections)

        # Process attendance with PPE verification
        detector._process_attendance(face_results_2)
        
        # Should have recorded attendance (all PPE present)
        assert len(detector.recent_detections) > initial_detections, "Should have added detection record"
        latest_detection = detector.recent_detections[-1]
        assert latest_detection['status'] == 'Present', f"Expected Present, got {latest_detection['status']}"
        assert latest_detection['ppe_mode'] == True, "Should be in PPE Verification mode"
        
        print("✅ PPE Verification Mode (compliant) works")
        
        # Test 3: PPE Verification Mode - Missing PPE
        detector._last_detections = [
            {'class_name': 'Hardhat', 'bbox': [110, 110, 150, 150]},
            {'class_name': 'NO-Mask', 'bbox': [130, 120, 160, 140]},  # Missing mask
            {'class_name': 'Safety Vest', 'bbox': [120, 150, 180, 190]}
        ]

        # Use third person to avoid cooldown
        face_results_3 = [{
            'recognized_person': 'Bob Wilson',
            'recognition_confidence': 88,
            'bbox': [100, 100, 200, 200]
        }]

        # Update mock to include Bob
        def mock_get_employee_extended(name):
            employees = {
                'John Doe': {'employee_id': 'EMP001', 'department': 'Engineering'},
                'Jane Smith': {'employee_id': 'EMP002', 'department': 'Safety'},
                'Bob Wilson': {'employee_id': 'EMP003', 'department': 'Operations'}
            }
            return employees.get(name)

        detector._get_employee_cached = mock_get_employee_extended

        initial_detections = len(detector.recent_detections)

        # Process attendance with missing PPE
        detector._process_attendance(face_results_3)
        
        # Should have denied access
        assert len(detector.recent_detections) > initial_detections, "Should have added detection record"
        latest_detection = detector.recent_detections[-1]
        assert latest_detection['status'] == 'Denied', f"Expected Denied, got {latest_detection['status']}"
        assert 'Mask' in latest_detection['reason'], "Should mention missing Mask"
        
        print("✅ PPE Verification Mode (non-compliant) works")
        
        print("🎉 Webcam Detector Logic tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Webcam Detector Logic test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration_management():
    """Test configuration management"""
    print("\n🧪 Testing Configuration Management...")
    
    try:
        from ppe_detection_engine import PPEDetectionEngine
        
        engine = PPEDetectionEngine()
        
        # Test different configurations
        configs_to_test = [
            {'Hardhat': True, 'Mask': False, 'Safety Vest': True},
            {'Hardhat': False, 'Mask': True, 'Safety Vest': False},
            {'Hardhat': True, 'Mask': True, 'Safety Vest': True},
            {'Hardhat': False, 'Mask': False, 'Safety Vest': False}
        ]
        
        for config in configs_to_test:
            engine.set_required_ppe_for_attendance(config)
            retrieved_config = engine.get_required_ppe_for_attendance()
            assert retrieved_config == config, f"Config mismatch: {config} != {retrieved_config}"
        
        print("✅ Configuration management works")
        print("🎉 Configuration Management tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Configuration Management test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting PPE Toggle Feature Tests...\n")
    
    tests = [
        test_ppe_detection_engine,
        test_webcam_detector_logic,
        test_configuration_management
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The PPE Toggle Feature is ready for use.")
        return True
    else:
        print("❌ Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
